<template>
  <div class="add-pet-container">
    <div class="card">
      <h1 class="page-title">
        <i class="fas fa-plus-circle"></i>
        添加新宠物
      </h1>
      
      <form @submit.prevent="addPet">
        <div class="form-group">
          <label>宠物名称</label>
          <input type="text" v-model="pet.name" required>
        </div>
        
        <div class="form-group">
          <label>种类</label>
          <select v-model="pet.species" required>
            <option value="狗">狗</option>
            <option value="猫">猫</option>
            <option value="兔">兔</option>
            <option value="鸟">鸟</option>
            <option value="其他">其他</option>
          </select>
        </div>
        
        <div class="form-group">
          <label>品种</label>
          <input type="text" v-model="pet.breed" required>
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label>年龄</label>
            <input type="number" v-model="pet.age" min="0" max="30" required>
          </div>
          <div class="form-group">
            <label>性别</label>
            <select v-model="pet.gender" required>
              <option value="雄性">雄性</option>
              <option value="雌性">雌性</option>
            </select>
          </div>
        </div>
        
        <div class="form-group">
          <label>健康状况</label>
          <input type="text" v-model="pet.health_status" required>
        </div>
        
        <div class="form-group">
          <label>描述</label>
          <textarea v-model="pet.description" rows="4" required></textarea>
        </div>
        
        <div class="form-actions">
          <button type="submit" class="btn btn-primary" :disabled="loading">
            <i class="fas fa-save"></i>
            {{ loading ? '添加中...' : '添加宠物' }}
          </button>
          <router-link to="/manage" class="btn btn-outline">
            <i class="fas fa-times"></i>
            取消
          </router-link>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { usePetsStore } from '@/stores/pets'

const router = useRouter()
const petsStore = usePetsStore()

// 加载状态
const loading = ref(false)

// 表单数据
const pet = reactive({
  name: '',
  species: '狗',
  breed: '',
  age: 1,
  gender: '雄性',
  healthStatus: '健康',
  description: '',
  isAdopted: false
})

// 添加宠物
const addPet = async () => {
  try {
    loading.value = true
    
    const result = await petsStore.addPet(pet)
    
    if (result.success) {
      ElMessage.success('宠物添加成功！')
      router.push('/manage')
    } else {
      ElMessage.error(result.message || '添加宠物失败')
    }
  } catch (error) {
    console.error('添加宠物失败:', error)
    ElMessage.error('添加宠物失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.add-pet-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.card {
  background: white;
  border-radius: 10px;
  box-shadow: var(--card-shadow);
  padding: 2rem;
}

.page-title {
  font-size: 2rem;
  margin-bottom: 2rem;
  color: var(--secondary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-title i {
  color: var(--accent);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: flex;
  gap: 1rem;
}

.form-row .form-group {
  flex: 1;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--secondary);
}

input, select, textarea {
  width: 100%;
  padding: 0.8rem 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border 0.3s;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(78, 137, 174, 0.2);
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #eee;
}

.btn {
  padding: 0.8rem 1.5rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
  font-size: 1rem;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--accent);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #e55a57;
  transform: translateY(-2px);
}

.btn-outline {
  background: transparent;
  border: 1px solid #ddd;
  color: #666;
}

.btn-outline:hover {
  background: #f5f5f5;
  border-color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .add-pet-container {
    padding: 1rem;
  }
  
  .card {
    padding: 1.5rem;
  }
  
  .form-row {
    flex-direction: column;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>

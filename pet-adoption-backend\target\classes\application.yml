# 宠物领养系统配置文件
server:
  port: 8080
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: pet-adoption-backend
  
  # 数据源配置
  datasource:
    url: ******************************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: PetAdoptionHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
    open-in-view: false
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
      enabled: true
  
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    serialization:
      write-dates-as-timestamps: false
    default-property-inclusion: non_null
    modules:
      - com.fasterxml.jackson.datatype.jsr310.JavaTimeModule

# 日志配置
logging:
  level:
    com.petadoption: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/pet-adoption.log

# JWT配置
jwt:
  secret: "4FRxGmBQWrg82xBYVufrhpxUJR/xfd4EYkmS5msrRlJt7+EyI/IKG7Nei/rW0/Okpy3pKAwBkzcwCFi6KClzeg=="
  expiration: 86400000  # 24 hours (milliseconds)
  header: Authorization
  prefix: "Bearer "

# 应用自定义配置
app:
  # 文件上传路径
  upload:
    path: uploads/
    pet-images: ${app.upload.path}pets/
    user-avatars: ${app.upload.path}avatars/
  
  # 分页配置
  pagination:
    default-page-size: 10
    max-page-size: 100
  
  # 管理员默认账户
  admin:
    username: admin
    password: admin123
    email: <EMAIL>
    phone: 13800138000

# 跨域配置
cors:
  allowed-origins: "http://localhost:3000,http://127.0.0.1:3000,http://localhost:3001,http://localhost:3002"
  allowed-methods: "GET,POST,PUT,DELETE,OPTIONS"
  allowed-headers: "*"
  allow-credentials: true
  max-age: 3600

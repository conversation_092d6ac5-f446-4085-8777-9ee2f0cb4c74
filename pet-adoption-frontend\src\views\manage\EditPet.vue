<template>
  <div class="edit-pet-container">
    <div class="page-header">
      <h1 class="page-title">
        <i class="fas fa-edit"></i>
        编辑宠物信息
      </h1>
      <p class="page-subtitle">修改宠物的详细信息</p>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <div v-else-if="petForm" class="edit-form-container">
      <el-form
        ref="petFormRef"
        :model="petForm"
        :rules="petRules"
        label-width="120px"
        class="pet-form"
      >
        <!-- 基本信息 -->
        <el-card class="form-section">
          <template #header>
            <div class="card-header">
              <i class="fas fa-info-circle"></i>
              <span>基本信息</span>
            </div>
          </template>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="宠物名称" prop="name">
                <el-input v-model="petForm.name" placeholder="请输入宠物名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="种类" prop="species">
                <el-select v-model="petForm.species" placeholder="请选择种类" style="width: 100%">
                  <el-option label="狗" value="狗" />
                  <el-option label="猫" value="猫" />
                  <el-option label="兔" value="兔" />
                  <el-option label="鸟" value="鸟" />
                  <el-option label="其他" value="其他" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="品种" prop="breed">
                <el-input v-model="petForm.breed" placeholder="请输入品种" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="年龄" prop="age">
                <el-input-number
                  v-model="petForm.age"
                  :min="0"
                  :max="30"
                  placeholder="请输入年龄"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="性别" prop="gender">
                <el-radio-group v-model="petForm.gender">
                  <el-radio label="雄性">雄性</el-radio>
                  <el-radio label="雌性">雌性</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="体重(kg)" prop="weight">
                <el-input-number
                  v-model="petForm.weight"
                  :min="0.1"
                  :max="100"
                  :precision="1"
                  placeholder="请输入体重"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="颜色" prop="color">
                <el-input v-model="petForm.color" placeholder="请输入颜色" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否绝育" prop="isNeutered">
                <el-switch v-model="petForm.isNeutered" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 健康信息 -->
        <el-card class="form-section">
          <template #header>
            <div class="card-header">
              <i class="fas fa-heart"></i>
              <span>健康信息</span>
            </div>
          </template>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="健康状况" prop="healthStatus">
                <el-select v-model="petForm.healthStatus" placeholder="请选择健康状况" style="width: 100%">
                  <el-option label="健康" value="健康" />
                  <el-option label="良好" value="良好" />
                  <el-option label="需要治疗" value="需要治疗" />
                  <el-option label="康复中" value="康复中" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="疫苗状况" prop="vaccinationStatus">
                <el-select v-model="petForm.vaccinationStatus" placeholder="请选择疫苗状况" style="width: 100%">
                  <el-option label="已完成" value="已完成" />
                  <el-option label="部分完成" value="部分完成" />
                  <el-option label="未接种" value="未接种" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="特殊需求" prop="specialNeeds">
            <el-input
              v-model="petForm.specialNeeds"
              type="textarea"
              :rows="3"
              placeholder="请描述宠物的特殊需求（如特殊饮食、药物等）"
            />
          </el-form-item>
        </el-card>

        <!-- 详细描述 -->
        <el-card class="form-section">
          <template #header>
            <div class="card-header">
              <i class="fas fa-file-text"></i>
              <span>详细描述</span>
            </div>
          </template>

          <el-form-item label="宠物描述" prop="description">
            <el-input
              v-model="petForm.description"
              type="textarea"
              :rows="4"
              placeholder="请详细描述宠物的性格、习惯等信息"
            />
          </el-form-item>

          <el-form-item label="性格特点" prop="personality">
            <el-input
              v-model="petForm.personality"
              placeholder="请描述宠物的性格特点"
            />
          </el-form-item>
        </el-card>

        <!-- 状态管理 -->
        <el-card class="form-section">
          <template #header>
            <div class="card-header">
              <i class="fas fa-cog"></i>
              <span>状态管理</span>
            </div>
          </template>

          <el-form-item label="领养状态" prop="isAdopted">
            <el-radio-group v-model="petForm.isAdopted">
              <el-radio :label="false">待领养</el-radio>
              <el-radio :label="true">已被领养</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-card>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button
            type="primary"
            size="large"
            :loading="submitting"
            @click="updatePet"
          >
            <i class="fas fa-save"></i>
            {{ submitting ? '更新中...' : '更新信息' }}
          </el-button>
          <el-button
            size="large"
            @click="router.push('/manage')"
          >
            <i class="fas fa-times"></i>
            取消
          </el-button>
        </div>
      </el-form>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <el-empty description="未找到宠物信息">
        <el-button type="primary" @click="router.push('/manage')">
          返回管理页面
        </el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { usePetsStore } from '@/stores/pets'

const router = useRouter()
const route = useRoute()
const petsStore = usePetsStore()

// 状态
const loading = ref(true)
const submitting = ref(false)
const petFormRef = ref()

// 宠物ID
const petId = route.params.id

// 表单数据
const petForm = reactive({
  name: '',
  species: '',
  breed: '',
  age: 0,
  gender: '',
  weight: 0,
  color: '',
  isNeutered: false,
  healthStatus: '',
  vaccinationStatus: '',
  specialNeeds: '',
  description: '',
  personality: '',
  isAdopted: false
})

// 表单验证规则
const petRules = {
  name: [
    { required: true, message: '请输入宠物名称', trigger: 'blur' },
    { min: 1, max: 50, message: '名称长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  species: [
    { required: true, message: '请选择宠物种类', trigger: 'change' }
  ],
  breed: [
    { required: true, message: '请输入品种', trigger: 'blur' }
  ],
  age: [
    { required: true, message: '请输入年龄', trigger: 'blur' },
    { type: 'number', min: 0, max: 30, message: '年龄必须在 0 到 30 之间', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  weight: [
    { required: true, message: '请输入体重', trigger: 'blur' },
    { type: 'number', min: 0.1, max: 100, message: '体重必须在 0.1 到 100 之间', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入宠物描述', trigger: 'blur' }
  ]
}

// 获取宠物详情
const fetchPet = async () => {
  try {
    loading.value = true
    const result = await petsStore.fetchPetDetail(petId)

    if (result.success && result.data) {
      // 将后端数据映射到表单
      const pet = result.data
      Object.assign(petForm, {
        name: pet.name || '',
        species: pet.species || '',
        breed: pet.breed || '',
        age: pet.age || 0,
        gender: pet.gender || '',
        weight: pet.weight || 0,
        color: pet.color || '',
        isNeutered: pet.isNeutered || pet.is_neutered || false,
        healthStatus: pet.healthStatus || pet.health_status || '',
        vaccinationStatus: pet.vaccinationStatus || pet.vaccination_status || '',
        specialNeeds: pet.specialNeeds || pet.special_needs || '',
        description: pet.description || '',
        personality: pet.personality || '',
        isAdopted: pet.isAdopted || pet.is_adopted || false
      })
    } else {
      ElMessage.error('获取宠物信息失败')
      router.push('/manage')
    }
  } catch (error) {
    console.error('获取宠物详情失败:', error)
    ElMessage.error('获取宠物信息失败')
    router.push('/manage')
  } finally {
    loading.value = false
  }
}

// 更新宠物信息
const updatePet = async () => {
  if (!petFormRef.value) return

  try {
    // 表单验证
    const valid = await petFormRef.value.validate()
    if (!valid) return

    submitting.value = true

    // 转换数据格式以匹配后端API
    const updateData = {
      name: petForm.name,
      species: petForm.species,
      breed: petForm.breed,
      age: petForm.age,
      gender: petForm.gender,
      weight: petForm.weight,
      color: petForm.color,
      is_neutered: petForm.isNeutered,
      health_status: petForm.healthStatus,
      vaccination_status: petForm.vaccinationStatus,
      special_needs: petForm.specialNeeds,
      description: petForm.description,
      personality: petForm.personality,
      is_adopted: petForm.isAdopted
    }

    const result = await petsStore.updatePet(petId, updateData)

    if (result.success) {
      ElMessage.success('宠物信息已更新！')
      router.push('/manage')
    } else {
      ElMessage.error(result.message || '更新失败')
    }
  } catch (error) {
    console.error('更新宠物失败:', error)
    ElMessage.error('更新失败')
  } finally {
    submitting.value = false
  }
}

onMounted(() => {
  fetchPet()
})
</script>

<style scoped>
.edit-pet-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
}

.page-header {
  text-align: center;
  margin-bottom: 2rem;
}

.page-title {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  color: #2c3e50;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.page-title i {
  color: #e74c3c;
}

.page-subtitle {
  color: #7f8c8d;
  font-size: 1.1rem;
}

.edit-form-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.form-section {
  margin-bottom: 2rem;
}

.form-section:last-of-type {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #2c3e50;
}

.card-header i {
  color: #3498db;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #ecf0f1;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-card) {
  border: 1px solid #ecf0f1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:deep(.el-card__header) {
  background-color: #f8f9fa;
  border-bottom: 1px solid #ecf0f1;
  padding: 1rem 1.5rem;
}

:deep(.el-card__body) {
  padding: 1.5rem;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #2c3e50;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--large) {
  padding: 12px 24px;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .edit-pet-container {
    padding: 1rem;
  }

  .edit-form-container {
    padding: 1.5rem;
  }

  .page-title {
    font-size: 2rem;
  }

  .form-actions {
    flex-direction: column;
  }

  :deep(.el-col) {
    margin-bottom: 1rem;
  }
}

@media (max-width: 480px) {
  .edit-pet-container {
    padding: 0.5rem;
  }

  .edit-form-container {
    padding: 1rem;
  }

  .page-title {
    font-size: 1.8rem;
  }

  :deep(.el-card__body) {
    padding: 1rem;
  }
}
</style>

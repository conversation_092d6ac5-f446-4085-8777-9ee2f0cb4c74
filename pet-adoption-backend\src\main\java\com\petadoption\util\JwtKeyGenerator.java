package com.petadoption.util;

import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import java.util.Base64;

/**
 * JWT密钥生成工具
 * 用于生成符合安全标准的JWT密钥
 * 
 * <AUTHOR> Team
 */
public class JwtKeyGenerator {
    
    public static void main(String[] args) {
        // 生成符合HS512算法要求的安全密钥
        byte[] keyBytes = Keys.secretKeyFor(SignatureAlgorithm.HS512).getEncoded();
        String base64Key = Base64.getEncoder().encodeToString(keyBytes);
        
        System.out.println("=================================");
        System.out.println("JWT安全密钥生成完成");
        System.out.println("=================================");
        System.out.println("密钥长度: " + (keyBytes.length * 8) + " bits");
        System.out.println("Base64编码密钥:");
        System.out.println(base64Key);
        System.out.println("=================================");
        System.out.println("请将此密钥复制到application.yml中的jwt.secret配置项");
        System.out.println("=================================");
    }
}

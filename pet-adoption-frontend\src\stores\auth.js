import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api/auth'
import { getToken, setToken, removeToken } from '@/utils/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref(null)
  const token = ref(getToken())
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')

  // 初始化认证状态
  const initAuth = async () => {
    if (token.value) {
      try {
        loading.value = true
        const userData = await authApi.getCurrentUser()
        user.value = userData
        console.log('认证初始化成功:', userData)
      } catch (error) {
        console.error('初始化认证失败:', error)
        // 如果token无效，清除认证状态
        if (error.response?.status === 401) {
          console.log('Token已过期，清除认证状态')
          logout()
        }
      } finally {
        loading.value = false
      }
    } else {
      console.log('没有找到token，跳过认证初始化')
    }
  }

  // 登录
  const login = async (credentials) => {
    try {
      loading.value = true
      const response = await authApi.login(credentials)
      
      token.value = response.token
      user.value = response.user
      
      setToken(response.token)
      
      return { success: true, user: response.user }
    } catch (error) {
      console.error('登录失败:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '登录失败，请检查用户名和密码' 
      }
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (userData) => {
    try {
      loading.value = true
      const response = await authApi.register(userData)
      
      return { success: true, message: '注册成功，请登录' }
    } catch (error) {
      console.error('注册失败:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '注册失败，请稍后重试' 
      }
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = () => {
    user.value = null
    token.value = null
    removeToken()
  }

  // 更新用户信息
  const updateUser = (userData) => {
    user.value = { ...user.value, ...userData }
  }

  return {
    // 状态
    user,
    token,
    loading,
    
    // 计算属性
    isAuthenticated,
    isAdmin,
    
    // 方法
    initAuth,
    login,
    register,
    logout,
    updateUser
  }
})

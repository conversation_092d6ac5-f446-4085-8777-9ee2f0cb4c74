<template>
  <div class="profile-container">
    <div class="profile-header">
      <h1 class="page-title">
        <i class="fas fa-user"></i>
        个人资料
      </h1>
      <p class="page-subtitle">管理您的账户信息和偏好设置</p>
    </div>

    <div class="profile-content">
      <!-- 用户信息卡片 -->
      <div class="profile-card">
        <div class="card-header">
          <div class="user-avatar-section">
            <div class="user-avatar large">
              {{ getUserAvatarLetter(authStore.user) }}
            </div>
            <div class="user-basic-info">
              <h2 class="username">{{ authStore.user?.username }}</h2>
              <p class="user-role">
                <i class="fas fa-shield-alt"></i>
                {{ authStore.user?.role === 'admin' ? '管理员' : '普通用户' }}
              </p>
              <p class="join-date">
                <i class="fas fa-calendar-alt"></i>
                加入时间：{{ formatDate(authStore.user?.create_time) }}
              </p>
            </div>
          </div>
        </div>

        <!-- 个人信息表单 -->
        <div class="card-body">
          <el-tabs v-model="activeTab" class="profile-tabs">
            <!-- 基本信息 -->
            <el-tab-pane label="基本信息" name="basic">
              <el-form
                ref="profileFormRef"
                :model="profileForm"
                :rules="profileRules"
                label-width="100px"
                class="profile-form"
              >
                <el-form-item label="用户名" prop="username">
                  <el-input
                    v-model="profileForm.username"
                    placeholder="请输入用户名"
                    :disabled="true"
                  >
                    <template #prefix>
                      <i class="fas fa-user"></i>
                    </template>
                  </el-input>
                  <div class="form-tip">用户名不可修改</div>
                </el-form-item>

                <el-form-item label="邮箱地址" prop="email">
                  <el-input
                    v-model="profileForm.email"
                    placeholder="请输入邮箱地址"
                  >
                    <template #prefix>
                      <i class="fas fa-envelope"></i>
                    </template>
                  </el-input>
                </el-form-item>

                <el-form-item label="手机号码" prop="phone">
                  <el-input
                    v-model="profileForm.phone"
                    placeholder="请输入手机号码"
                  >
                    <template #prefix>
                      <i class="fas fa-phone"></i>
                    </template>
                  </el-input>
                </el-form-item>

                <el-form-item>
                  <el-button
                    type="primary"
                    :loading="updating"
                    @click="updateProfile"
                  >
                    <i class="fas fa-save"></i>
                    保存修改
                  </el-button>
                  <el-button @click="resetForm">
                    <i class="fas fa-undo"></i>
                    重置
                  </el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>

            <!-- 修改密码 -->
            <el-tab-pane label="修改密码" name="password">
              <el-form
                ref="passwordFormRef"
                :model="passwordForm"
                :rules="passwordRules"
                label-width="100px"
                class="password-form"
              >
                <el-form-item label="当前密码" prop="oldPassword">
                  <el-input
                    v-model="passwordForm.oldPassword"
                    type="password"
                    placeholder="请输入当前密码"
                    show-password
                  >
                    <template #prefix>
                      <i class="fas fa-lock"></i>
                    </template>
                  </el-input>
                </el-form-item>

                <el-form-item label="新密码" prop="newPassword">
                  <el-input
                    v-model="passwordForm.newPassword"
                    type="password"
                    placeholder="请输入新密码"
                    show-password
                  >
                    <template #prefix>
                      <i class="fas fa-key"></i>
                    </template>
                  </el-input>
                </el-form-item>

                <el-form-item label="确认密码" prop="confirmPassword">
                  <el-input
                    v-model="passwordForm.confirmPassword"
                    type="password"
                    placeholder="请确认新密码"
                    show-password
                  >
                    <template #prefix>
                      <i class="fas fa-key"></i>
                    </template>
                  </el-input>
                </el-form-item>

                <el-form-item>
                  <el-button
                    type="primary"
                    :loading="changingPassword"
                    @click="changePassword"
                  >
                    <i class="fas fa-shield-alt"></i>
                    修改密码
                  </el-button>
                  <el-button @click="resetPasswordForm">
                    <i class="fas fa-undo"></i>
                    重置
                  </el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>

            <!-- 账户统计 -->
            <el-tab-pane label="账户统计" name="stats">
              <div class="stats-section">
                <div class="stats-grid">
                  <div class="stat-card">
                    <div class="stat-icon">
                      <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="stat-content">
                      <div class="stat-number">{{ userStats.totalApplications }}</div>
                      <div class="stat-label">总申请数</div>
                    </div>
                  </div>
                  <div class="stat-card">
                    <div class="stat-icon">
                      <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                      <div class="stat-number">{{ userStats.approvedApplications }}</div>
                      <div class="stat-label">通过申请</div>
                    </div>
                  </div>
                  <div class="stat-card">
                    <div class="stat-icon">
                      <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                      <div class="stat-number">{{ userStats.pendingApplications }}</div>
                      <div class="stat-label">待审核</div>
                    </div>
                  </div>
                  <div class="stat-card">
                    <div class="stat-icon">
                      <i class="fas fa-heart"></i>
                    </div>
                    <div class="stat-content">
                      <div class="stat-number">{{ userStats.adoptedPets }}</div>
                      <div class="stat-label">成功领养</div>
                    </div>
                  </div>
                </div>

                <div class="recent-activity">
                  <h3 class="activity-title">
                    <i class="fas fa-history"></i>
                    最近活动
                  </h3>
                  <div class="activity-list">
                    <div 
                      v-for="activity in recentActivities" 
                      :key="activity.id"
                      class="activity-item"
                    >
                      <div class="activity-icon">
                        <i :class="activity.icon"></i>
                      </div>
                      <div class="activity-content">
                        <p class="activity-text">{{ activity.text }}</p>
                        <span class="activity-time">{{ formatRelativeTime(activity.time) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { authApi } from '@/api/auth'
import { getUserAvatarLetter } from '@/utils/auth'
import { formatDate, formatRelativeTime, validateEmail, validatePhone } from '@/utils'

const authStore = useAuthStore()

// 表单引用
const profileFormRef = ref()
const passwordFormRef = ref()

// 活动标签页
const activeTab = ref('basic')

// 加载状态
const updating = ref(false)
const changingPassword = ref(false)

// 个人信息表单
const profileForm = reactive({
  username: '',
  email: '',
  phone: ''
})

// 密码表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 用户统计
const userStats = ref({
  totalApplications: 0,
  approvedApplications: 0,
  pendingApplications: 0,
  adoptedPets: 0
})

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    icon: 'fas fa-file-alt',
    text: '提交了对宠物"豆豆"的领养申请',
    time: '2023-11-20T10:30:00'
  },
  {
    id: 2,
    icon: 'fas fa-check-circle',
    text: '领养申请已通过审核',
    time: '2023-11-18T14:20:00'
  },
  {
    id: 3,
    icon: 'fas fa-user-edit',
    text: '更新了个人资料',
    time: '2023-11-15T09:15:00'
  }
])

// 表单验证规则
const profileRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { validator: (rule, value, callback) => {
      if (!validateEmail(value)) {
        callback(new Error('请输入有效的邮箱地址'))
      } else {
        callback()
      }
    }, trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { validator: (rule, value, callback) => {
      if (!validatePhone(value)) {
        callback(new Error('请输入有效的手机号码'))
      } else {
        callback()
      }
    }, trigger: 'blur' }
  ]
}

const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' },
    { pattern: /^(?=.*[a-zA-Z])(?=.*\d)/, message: '密码必须包含字母和数字', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    { validator: (rule, value, callback) => {
      if (value !== passwordForm.newPassword) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }, trigger: 'blur' }
  ]
}

// 初始化表单数据
const initFormData = () => {
  if (authStore.user) {
    profileForm.username = authStore.user.username
    profileForm.email = authStore.user.email
    profileForm.phone = authStore.user.phone
  }
}

// 更新个人资料
const updateProfile = async () => {
  if (!profileFormRef.value) return
  
  try {
    await profileFormRef.value.validate()
    updating.value = true
    
    const result = await authApi.updateProfile({
      email: profileForm.email,
      phone: profileForm.phone
    })
    
    // 更新本地用户信息
    authStore.updateUser(result)
    
    ElMessage.success('个人资料更新成功')
  } catch (error) {
    console.error('更新个人资料失败:', error)
    ElMessage.error('更新失败，请稍后重试')
  } finally {
    updating.value = false
  }
}

// 修改密码
const changePassword = async () => {
  if (!passwordFormRef.value) return
  
  try {
    await passwordFormRef.value.validate()
    changingPassword.value = true
    
    await authApi.changePassword({
      oldPassword: passwordForm.oldPassword,
      newPassword: passwordForm.newPassword,
      confirmPassword: passwordForm.confirmPassword
    })
    
    ElMessage.success('密码修改成功')
    resetPasswordForm()
  } catch (error) {
    console.error('修改密码失败:', error)
    ElMessage.error('修改失败，请检查当前密码是否正确')
  } finally {
    changingPassword.value = false
  }
}

// 重置表单
const resetForm = () => {
  initFormData()
}

const resetPasswordForm = () => {
  passwordForm.oldPassword = ''
  passwordForm.newPassword = ''
  passwordForm.confirmPassword = ''
  if (passwordFormRef.value) {
    passwordFormRef.value.clearValidate()
  }
}

// 获取用户统计数据
const fetchUserStats = async () => {
  try {
    // 这里可以调用API获取真实统计数据
    // const stats = await userApi.getUserStats()
    // userStats.value = stats
    
    // 模拟数据
    userStats.value = {
      totalApplications: 3,
      approvedApplications: 1,
      pendingApplications: 1,
      adoptedPets: 1
    }
  } catch (error) {
    console.error('获取用户统计失败:', error)
  }
}

onMounted(() => {
  initFormData()
  fetchUserStats()
})
</script>

<style scoped>
.profile-container {
  max-width: 800px;
  margin: 0 auto;
}

.profile-header {
  text-align: center;
  margin-bottom: 2rem;
}

.page-title {
  font-size: 2rem;
  color: var(--secondary);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.page-title i {
  color: var(--accent);
}

.page-subtitle {
  color: #666;
  font-size: 1rem;
}

.profile-card {
  background: white;
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  overflow: hidden;
}

.card-header {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  padding: 2rem;
}

.user-avatar-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.user-avatar.large {
  width: 80px;
  height: 80px;
  font-size: 2rem;
  background: rgba(255, 255, 255, 0.2);
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.username {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.user-role,
.join-date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.3rem;
  opacity: 0.9;
}

.card-body {
  padding: 2rem;
}

.profile-tabs {
  --el-tabs-header-height: 50px;
}

.form-tip {
  font-size: 0.8rem;
  color: #999;
  margin-top: 0.3rem;
}

.stats-section {
  padding: 1rem 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--light);
  padding: 1.5rem;
  border-radius: 8px;
  text-align: center;
  transition: var(--transition);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  margin: 0 auto 1rem;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--primary);
  margin-bottom: 0.3rem;
}

.stat-label {
  color: #666;
  font-size: 0.9rem;
}

.recent-activity {
  background: var(--light);
  padding: 1.5rem;
  border-radius: 8px;
}

.activity-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: var(--secondary);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 6px;
}

.activity-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-text {
  margin-bottom: 0.3rem;
  color: var(--dark);
}

.activity-time {
  font-size: 0.8rem;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-avatar-section {
    flex-direction: column;
    text-align: center;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .activity-item {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
